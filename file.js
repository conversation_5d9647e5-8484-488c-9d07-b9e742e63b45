jsonInput = [
    {
        "parent_id": "200a7d56809280a9bc89c3203821338c",
        "id": "200a7d56-8092-8054-97ba-ee3ada860d30",
        "type": "heading_1",
        "root_id": "200a7d56809280a9bc89c3203821338c",
        "content": "heading",
        "table_row": null
    },
    {
        "parent_id": "200a7d56809280a9bc89c3203821338c",
        "id": "200a7d56-8092-8022-8429-f4afee2412cd",
        "type": "paragraph",
        "root_id": "200a7d56809280a9bc89c3203821338c",
        "content": "",
        "table_row": null
    },
    {
        "parent_id": "200a7d56809280a9bc89c3203821338c",
        "id": "200a7d56-8092-803c-9168-e3cc79f83c3e",
        "type": "paragraph",
        "root_id": "200a7d56809280a9bc89c3203821338c",
        "content": "content content ",
        "table_row": null
    },
    {
        "parent_id": "200a7d56809280a9bc89c3203821338c",
        "id": "200a7d56-8092-801d-aeba-c40cc275feb1",
        "type": "table",
        "root_id": "200a7d56809280a9bc89c3203821338c",
        "content": null,
        "table_row": null
    },
    {
        "parent_id": "200a7d56-8092-801d-aeba-c40cc275feb1",
        "id": "200a7d56-8092-80c1-9535-e5055f69cce8",
        "type": "table_row",
        "root_id": "200a7d56809280a9bc89c3203821338c",
        "content": null,
        "table_row": "{\"cells\":[[{\"type\":\"text\",\"text\":{\"content\":\"column1\",\"link\":null},\"annotations\":{\"bold\":false,\"italic\":false,\"strikethrough\":false,\"underline\":false,\"code\":false,\"color\":\"default\"},\"plain_text\":\"column1\",\"href\":null}],[{\"type\":\"text\",\"text\":{\"content\":\"column2\",\"link\":null},\"annotations\":{\"bold\":false,\"italic\":false,\"strikethrough\":false,\"underline\":false,\"code\":false,\"color\":\"default\"},\"plain_text\":\"column2\",\"href\":null}],[{\"type\":\"text\",\"text\":{\"content\":\"column3\",\"link\":null},\"annotations\":{\"bold\":false,\"italic\":false,\"strikethrough\":false,\"underline\":false,\"code\":false,\"color\":\"default\"},\"plain_text\":\"column3\",\"href\":null}]]}"
    },
    {
        "parent_id": "200a7d56-8092-801d-aeba-c40cc275feb1",
        "id": "200a7d56-8092-8093-8dad-c97283252ffd",
        "type": "table_row",
        "root_id": "200a7d56809280a9bc89c3203821338c",
        "content": null,
        "table_row": "{\"cells\":[[{\"type\":\"text\",\"text\":{\"content\":\"1\",\"link\":null},\"annotations\":{\"bold\":false,\"italic\":false,\"strikethrough\":false,\"underline\":false,\"code\":false,\"color\":\"default\"},\"plain_text\":\"1\",\"href\":null}],[{\"type\":\"text\",\"text\":{\"content\":\"2\",\"link\":null},\"annotations\":{\"bold\":false,\"italic\":false,\"strikethrough\":false,\"underline\":false,\"code\":false,\"color\":\"default\"},\"plain_text\":\"2\",\"href\":null}],[{\"type\":\"text\",\"text\":{\"content\":\"5\",\"link\":null},\"annotations\":{\"bold\":false,\"italic\":false,\"strikethrough\":false,\"underline\":false,\"code\":false,\"color\":\"default\"},\"plain_text\":\"5\",\"href\":null}]]}"
    },
    {
        "parent_id": "200a7d56-8092-801d-aeba-c40cc275feb1",
        "id": "200a7d56-8092-80cd-8d95-e8272e2844a6",
        "type": "table_row",
        "root_id": "200a7d56809280a9bc89c3203821338c",
        "content": null,
        "table_row": "{\"cells\":[[{\"type\":\"text\",\"text\":{\"content\":\"3\",\"link\":null},\"annotations\":{\"bold\":false,\"italic\":false,\"strikethrough\":false,\"underline\":false,\"code\":false,\"color\":\"default\"},\"plain_text\":\"3\",\"href\":null}],[{\"type\":\"text\",\"text\":{\"content\":\"4\",\"link\":null},\"annotations\":{\"bold\":false,\"italic\":false,\"strikethrough\":false,\"underline\":false,\"code\":false,\"color\":\"default\"},\"plain_text\":\"4\",\"href\":null}],[{\"type\":\"text\",\"text\":{\"content\":\"6\",\"link\":null},\"annotations\":{\"bold\":false,\"italic\":false,\"strikethrough\":false,\"underline\":false,\"code\":false,\"color\":\"default\"},\"plain_text\":\"6\",\"href\":null}]]}"
    },
    {
        "parent_id": "200a7d56809280a9bc89c3203821338c",
        "id": "200a7d56-8092-80a5-9c5f-e4efe2730213",
        "type": "paragraph",
        "root_id": "200a7d56809280a9bc89c3203821338c",
        "content": "",
        "table_row": null
    },
    {
        "parent_id": "200a7d56809280a9bc89c3203821338c",
        "id": "200a7d56-8092-804d-8732-e2a53a29c18b",
        "type": "paragraph",
        "root_id": "200a7d56809280a9bc89c3203821338c",
        "content": "",
        "table_row": null
    },
    {
        "parent_id": "200a7d56809280a9bc89c3203821338c",
        "id": "200a7d56-8092-80a7-9dac-f37a52266e2b",
        "type": "paragraph",
        "root_id": "200a7d56809280a9bc89c3203821338c",
        "content": "",
        "table_row": null
    }
]

// turn the data into a markdown file

function convertToMarkdown(data) {
    let markdown = '';
    let tableContent = [];
    let newTable = true;
    tableRootId = null;
    for (let i = 0; i < data.length; i++) {
        if (data[i].type === 'table_row') {
            if (newTable) {
                tableContent = [];
                newTable = false;
                tableRootId = data[i].root_id;
                let tableRow = JSON.parse(data[i].table_row).cells;
                console.log(tableRow);
            }
            
        }
    }
    return markdown;
}

console.log(convertToMarkdown(jsonInput));
